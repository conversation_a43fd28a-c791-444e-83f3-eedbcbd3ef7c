import { GoogleGenerativeAI } from '@google/generative-ai'
import { readBody, defineEventHandler } from "h3";

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const prompt = body.prompt || 'Write a social media post about innovation.'
  const tone = body.tone || 'professional'

  try {
    const genAI = new GoogleGenerativeAI(useRuntimeConfig().geminiApiKey)

    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' })

    const geminiPrompt = `You are a helpful assistant that writes ${tone} social media posts and generates relevant hashtags.
    Generate a ${tone} social media post about: ${prompt}
    Please format your response as follows:
    - First, write the social media post content
    - Then, on a new line, add relevant hashtags starting with #
    Keep the post engaging and under 280 characters if possible.`

    const result = await model.generateContent(geminiPrompt)
    const response = result.response
    const content = response.text() || ''

    const lines = content.split('\n').filter(line => line.trim())
    let text = ''
    let hashtags = ''

    for (const line of lines) {
      if (line.trim().startsWith('#')) {
        hashtags += (hashtags ? ' ' : '') + line.trim()
      } else if (line.trim() && !text) {
        text = line.trim()
      } else if (line.trim() && text && !hashtags) {
        text += ' ' + line.trim()
      }
    }

    if (!hashtags) {
      const match = content.match(/(.*)(#.+)/s)
      text = match?.[1]?.trim() || content.trim()
      hashtags = match?.[2]?.trim() || ''
    }

    return { text: text || content.trim(), hashtags }
  } catch (error) {
    console.error('Gemini API error:', error)
    return {
      text: 'Sorry, there was an error generating your post. Please try again.',
      hashtags: ''
    }
  }
});
