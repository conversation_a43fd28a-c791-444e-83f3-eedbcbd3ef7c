import { GoogleGenerativeAI } from '@google/generative-ai'
import { readBody, define<PERSON><PERSON><PERSON><PERSON><PERSON>, getHeader, createError } from "h3";

// Simple in-memory rate limiting (in production, use Redis or similar)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 10 // Max 10 requests per minute per IP

function checkRateLimit(ip: string): boolean {
  const now = Date.now()
  const userLimit = rateLimitMap.get(ip)

  if (!userLimit || now > userLimit.resetTime) {
    rateLimitMap.set(ip, { count: 1, resetTime: now + RATE_LIMIT_WINDOW })
    return true
  }

  if (userLimit.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }

  userLimit.count++
  return true
}

function validateOrigin(event: any): boolean {
  const origin = getHeader(event, 'origin')
  const referer = getHeader(event, 'referer')
  const host = getHeader(event, 'host')
  const userAgent = getHeader(event, 'user-agent')

  // Log for debugging (remove in production)
  // console.log('🔍 Origin validation:', { origin, referer, host, userAgent })

  // Allow requests from the same host (localhost in development)
  const allowedOrigins = [
    `http://localhost:3000`,
    `https://localhost:3000`,
    `http://127.0.0.1:3000`,
    `https://127.0.0.1:3000`
  ]

  // In production, add your actual domain
  if (process.env.NODE_ENV === 'production') {
    // Add your production domain here
    // allowedOrigins.push('https://yourdomain.com')
  }

  // Check origin header first (most reliable)
  if (origin && allowedOrigins.includes(origin)) {
    return true
  }

  // Check referer header as fallback
  if (referer) {
    try {
      const refererUrl = new URL(referer)
      const refererOrigin = `${refererUrl.protocol}//${refererUrl.host}`
      if (allowedOrigins.includes(refererOrigin)) {
        return true
      }
    } catch (error) {
      // Invalid referer URL
    }
  }

  // For development: Allow requests from browsers on same host
  // But be more strict - require either origin or referer header
  if (host && (host === 'localhost:3000' || host === '127.0.0.1:3000')) {
    // Only allow if it looks like a browser request (has user-agent and no curl)
    if (userAgent && !userAgent.includes('curl') && (origin || referer)) {
      return true
    }
  }

  return false
}

export default defineEventHandler(async (event) => {
  // Security checks
  const clientIP = getHeader(event, 'x-forwarded-for') ||
                   getHeader(event, 'x-real-ip') ||
                   event.node.req.socket?.remoteAddress ||
                   'unknown'

  // Rate limiting
  if (!checkRateLimit(clientIP as string)) {
    throw createError({
      statusCode: 429,
      statusMessage: 'Too Many Requests - Rate limit exceeded'
    })
  }

  // Origin validation
  if (!validateOrigin(event)) {
    throw createError({
      statusCode: 403,
      statusMessage: 'Forbidden - Invalid origin'
    })
  }

  // Validate request method
  if (event.node.req.method !== 'POST') {
    throw createError({
      statusCode: 405,
      statusMessage: 'Method Not Allowed'
    })
  }

  const body = await readBody(event)
  const prompt = body.prompt || 'Write a social media post about innovation.'
  const tone = body.tone || 'professional'

  // Validate input
  if (typeof prompt !== 'string' || prompt.trim().length === 0) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Bad Request - Invalid prompt'
    })
  }

  if (typeof tone !== 'string' || !['professional', 'casual', 'witty', 'inspirational'].includes(tone)) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Bad Request - Invalid tone'
    })
  }

  // Prevent excessively long prompts
  if (prompt.length > 500) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Bad Request - Prompt too long'
    })
  }

  try {
    const config = useRuntimeConfig()
    const genAI = new GoogleGenerativeAI(config.geminiApiKey)

    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' })

    const geminiPrompt = `You are a helpful assistant that writes ${tone} social media posts and generates relevant hashtags.
    Generate a ${tone} social media post about: ${prompt}
    Please format your response as follows:
    - First, write the social media post content
    - Then, on a new line, add relevant hashtags starting with #
    Keep the post engaging and under 280 characters if possible.`

    const result = await model.generateContent(geminiPrompt)
    const response = result.response
    const content = response.text() || ''

    const lines = content.split('\n').filter(line => line.trim())
    let text = ''
    let hashtags = ''

    for (const line of lines) {
      if (line.trim().startsWith('#')) {
        hashtags += (hashtags ? ' ' : '') + line.trim()
      } else if (line.trim() && !text) {
        text = line.trim()
      } else if (line.trim() && text && !hashtags) {
        text += ' ' + line.trim()
      }
    }

    if (!hashtags) {
      const match = content.match(/(.*)(#.+)/s)
      text = match?.[1]?.trim() || content.trim()
      hashtags = match?.[2]?.trim() || ''
    }

    return { text: text || content.trim(), hashtags }
  } catch (error) {
    console.error('Gemini API error:', error)
    return {
      text: 'Sorry, there was an error generating your post. Please try again.',
      hashtags: ''
    }
  }
});
